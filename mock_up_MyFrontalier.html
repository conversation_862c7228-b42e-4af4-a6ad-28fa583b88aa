<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>MyFrontaliers Web Kit</title>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
            --color-primary: #2a5b8c;
            --color-secondary: #4a90e2;
            --color-success: #2e8b57;
            --color-success-light: #50c878;
            --color-cta: #ff6b35;
            --color-background: #f8f9fa;
            --color-text-primary: #1d2d35;
            --color-text-secondary: #6c757d;
            --color-border: #dee2e6;
            --status-todo: #ff6b35;
            --status-inprogress: #4a90e2;
            --status-waiting: #ffc107;
            --status-completed: #2e8b57;
        }
        body {
            font-family: "Roboto", sans-serif;
            background-color: var(--color-background);
            color: var(--color-text-primary);
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-weight: 500;
            font-size: 0.75rem;
            text-transform: capitalize;
        }
        .status-todo {
            background-color: var(--status-todo);
            color: white;
        }
        .status-inprogress {
            background-color: var(--status-inprogress);
            color: white;
        }
        .status-waiting {
            background-color: var(--status-waiting);
            color: var(--color-text-primary);
        }
        .status-completed {
            background-color: var(--status-completed);
            color: white;
        }
        .active-tab {
            background-color: #e1f0ff;
            color: var(--color-primary);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #e9ecef;
        }
        .progress-value {
            height: 100%;
            border-radius: 4px;
        }
        .faq-question {
            cursor: pointer;
            padding: 1rem;
            border-bottom: 1px solid var(--color-border);
            font-weight: 500;
        }
        .faq-answer {
            padding: 1rem;
            background-color: #f8f9fa;
            display: none;
        }
        .drag-drop-area {
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .drag-drop-area:hover, .drag-drop-area.dragover {
            border-color: var(--color-secondary);
            background-color: #e9f7ff;
        }
    </style>
</head>
<body>
    <div class="flex h-screen bg-[var(--color-background)]">
        <!-- Barre de navigation latérale -->
        <aside class="w-64 flex-shrink-0 bg-white shadow-md">
            <div class="flex h-full flex-col p-4">
                <div class="mb-8 p-2">
                    <h1 class="text-xl font-bold text-[var(--color-primary)]">MyFrontaliers</h1>
                    <p class="text-xs text-[var(--color-text-secondary)]">Cabinet Expert Comptable</p>
                </div>
                <nav class="flex flex-col gap-2">
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="cabinet">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">Cabinet</span>
                    </a>
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="fiscalite">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">Fiscalité</span>
                    </a>
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="social">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">Social</span>
                    </a>
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="depot">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">Dépôt de Documents</span>
                    </a>
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="actualites">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3h2m-4 3h2M5 10h2a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1v-1a1 1 0 011-1z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">Actualités</span>
                    </a>
                    <a class="flex items-center gap-3 rounded-md px-3 py-2 text-[var(--color-text-secondary)] hover:bg-gray-100 hover:text-[var(--color-primary)] tab-button" data-tab="faq">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.79 4 4 0 1.976-1.724 3.5-3.772 3.5-1.742 0-3.223-.835-3.772-2M12 18.5v.01M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
                        </svg>
                        <span class="font-medium">FAQ</span>
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Contenu principal -->
        <main class="flex-1 overflow-y-auto">
            <!-- Onglet Cabinet -->
            <div class="p-8 tab-content active" id="cabinet">
                <header class="mb-8 flex items-center justify-between">
                    <h1 class="text-4xl font-bold text-gray-800">Tableau de Bord</h1>
                    <div class="flex items-center gap-4">
                        <button class="flex items-center gap-2 rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white shadow-sm transition-colors hover:bg-[var(--color-primary)]">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path clip-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" fill-rule="evenodd"></path>
                            </svg>
                            Nouveau Client
                        </button>
                        <button class="flex items-center gap-2 rounded-lg border border-[var(--color-border)] bg-white px-4 py-2 text-[var(--color-text-secondary)] shadow-sm transition-colors hover:bg-gray-50">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.75 2.75a.75.75 0 00-1.5 0v8.5a.75.75 0 001.5 0v-8.5z"></path>
                                <path clip-rule="evenodd" d="M3.25 2a.75.75 0 000 1.5h13.5a.75.75 0 000-1.5H3.25zM2.5 13.5A.5.5 0 013 13h14a.5.5 0 01.5.5v1a.5.5 0 01-.5.5H3a.5.5 0 01-.5-.5v-1z" fill-rule="evenodd"></path>
                            </svg>
                            Lettre de Mission
                        </button>
                    </div>
                </header>
                <section class="rounded-lg bg-white p-6 shadow-sm">
                    <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
                        <h2 class="text-2xl font-bold text-gray-800">Suivi des Dossiers</h2>
                        <div class="flex gap-3">
                            <select class="rounded-lg border-[var(--color-border)] text-sm focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                <option>Collaborateur</option>
                                <option>Marie Dubois</option>
                                <option>Pierre Martin</option>
                                <option>Sophie Leclerc</option>
                            </select>
                            <select class="rounded-lg border-[var(--color-border)] text-sm focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                <option>Canton</option>
                                <option>Genève</option>
                                <option>Vaud</option>
                                <option>Neuchâtel</option>
                            </select>
                            <select class="rounded-lg border-[var(--color-border)] text-sm focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                <option>Statut</option>
                                <option>À faire</option>
                                <option>En cours</option>
                                <option>En attente client</option>
                                <option>Terminé</option>
                            </select>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr class="border-b border-[var(--color-border)] bg-gray-50">
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Client</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Mission</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Canton</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Assigné</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Statut</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Rentabilité</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-[var(--color-border)]">
                                <tr>
                                    <td class="px-4 py-3 font-medium">Client A</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Déclaration Fiscale 2024</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Genève</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Marie Dubois</td>
                                    <td class="px-4 py-3"><span class="status-badge status-todo">À faire</span></td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-2">
                                            <div class="progress-bar w-24">
                                                <div class="progress-value bg-[var(--color-cta)]" style="width: 15%"></div>
                                            </div>
                                            <span>15%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Client B</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Conseil Fiscal</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Vaud</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Pierre Martin</td>
                                    <td class="px-4 py-3"><span class="status-badge status-inprogress">En cours</span></td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-2">
                                            <div class="progress-bar w-24">
                                                <div class="progress-value bg-[var(--color-secondary)]" style="width: 22%"></div>
                                            </div>
                                            <span>22%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Client C</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Optimisation Fiscale</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Neuchâtel</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Sophie Leclerc</td>
                                    <td class="px-4 py-3"><span class="status-badge status-waiting">En attente Client</span></td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-2">
                                            <div class="progress-bar w-24">
                                                <div class="progress-value bg-yellow-500" style="width: 10%"></div>
                                            </div>
                                            <span>10%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Client D</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Déclaration Fiscale 2024</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Genève</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">Marie Dubois</td>
                                    <td class="px-4 py-3"><span class="status-badge status-completed">Terminé</span></td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-2">
                                            <div class="progress-bar w-24">
                                                <div class="progress-value bg-[var(--color-success)]" style="width: 28%"></div>
                                            </div>
                                            <span>28%</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
                <section class="mt-8">
                    <h2 class="mb-4 text-2xl font-bold text-gray-800">Formations à Venir</h2>
                    <div class="flex items-center justify-between rounded-lg bg-cover bg-center p-6 text-white shadow-lg" style='background-image: linear-gradient(rgba(42, 91, 140, 0.85), rgba(42, 91, 140, 0.85)), url("https://lh3.googleusercontent.com/aida-public/AB6AXuCcehdyZFCm14IdhLQ5N0bkuNbYcVehGzTSZto1UebAlAt5wglxLZFys2CbJxlOfp7GvrkZjjO275TClymbb8nBJnuAJlixB9bLSSa9g-DosjGMPzYCSde_phIpM0kygNOMNHE1bSlP5NIc3SCSXMXAjt0r-ANalAK4gEFeDAD7rbiQ37jBTiurYAq8sOSm-edH9g7mIxZHm8osprNdJKl77nSITWCzI0PvmSsJzgsL5hovTNEmNqAwsv4tkEPd_-B3rfBMZnfmZDA");'>
                        <div>
                            <p class="text-sm uppercase tracking-wider">Prochain module</p>
                            <p class="text-xl font-bold">Optimisation fiscale transfrontalière</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm uppercase tracking-wider">Date limite</p>
                            <p class="text-xl font-bold">30/09/2025</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Onglet Fiscalité -->
            <div class="p-8 tab-content" id="fiscalite">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-gray-800">Fiscalité</h1>
                    <p class="text-[var(--color-text-secondary)] mt-2">Outils d'optimisation fiscale pour les travailleurs frontaliers</p>
                </header>

                <section class="rounded-lg bg-white p-6 shadow-sm mb-8">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">Simulateur Quasi-Résident (Genève)</h2>
                        <p class="text-[var(--color-text-secondary)]">Estimez l'économie potentielle avec le statut de quasi-résident</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Revenus bruts en Suisse (CHF)</label>
                                <input type="number" value="120000" class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Autres revenus (CHF)</label>
                                <input type="number" value="15000" class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Situation familiale</label>
                                <select class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                    <option>Célibataire</option>
                                    <option selected>Marié(e)</option>
                                    <option>Divorcé(e)</option>
                                    <option>Veuf/Veuve</option>
                                </select>
                            </div>
                            
                            <div class="mt-6">
                                <h3 class="font-medium mb-2">Déductions possibles</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox" checked class="rounded border-[var(--color-border)] text-[var(--color-secondary)]">
                                        <span>Frais de transport</span>
                                    </label>
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox" class="rounded border-[var(--color-border)] text-[var(--color-secondary)]">
                                        <span>Frais de repas</span>
                                    </label>
                                    <label class="flex items-center gap-2">
                                        <input type="checkbox" checked class="rounded border-[var(--color-border)] text-[var(--color-secondary)]">
                                        <span>Frais de formation</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">Résultats de simulation</h3>
                            
                            <div class="mb-6">
                                <div class="flex justify-between mb-2">
                                    <span>Impôt sans optimisation</span>
                                    <span class="font-bold">32 450 CHF</span>
                                </div>
                                <div class="h-3 w-full bg-gray-200 rounded-full overflow-hidden">
                                    <div class="h-full bg-[var(--color-secondary)]" style="width: 100%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <div class="flex justify-between mb-2">
                                    <span>Impôt avec statut Quasi-Résident</span>
                                    <span class="font-bold">19 750 CHF</span>
                                </div>
                                <div class="h-3 w-full bg-gray-200 rounded-full overflow-hidden">
                                    <div class="h-full bg-[var(--color-success)]" style="width: 61%"></div>
                                </div>
                            </div>
                            
                            <div class="bg-[var(--color-cta)] text-white rounded-lg p-4 mb-6">
                                <div class="text-center">
                                    <p class="text-sm">Économie potentielle</p>
                                    <p class="text-2xl font-bold">12 700 CHF</p>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h4 class="font-medium mb-2">Déductions applicables</h4>
                                <ul class="space-y-2">
                                    <li class="flex items-center gap-2">
                                        <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Frais de transport (3 500 CHF)
                                    </li>
                                    <li class="flex items-center gap-2">
                                        <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Frais de formation (1 200 CHF)
                                    </li>
                                </ul>
                            </div>
                            
                            <button class="w-full rounded-lg bg-[var(--color-secondary)] px-4 py-3 text-white font-medium hover:bg-[var(--color-primary)] transition-colors">
                                Générer le rapport détaillé
                            </button>
                        </div>
                    </div>
                </section>
                
                <section class="rounded-lg bg-white p-6 shadow-sm">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Arbre de décision : Quel statut fiscal pour mon client ?</h2>
                    
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="max-w-3xl mx-auto">
                            <div class="flex flex-col items-center mb-8">
                                <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                    1
                                </div>
                                <h3 class="text-lg font-bold mb-2">Client domicilié en France ?</h3>
                                <div class="flex gap-4">
                                    <button class="px-4 py-2 bg-[var(--color-success)] text-white rounded">Oui</button>
                                    <button class="px-4 py-2 bg-gray-300 rounded">Non</button>
                                </div>
                            </div>
                            
                            <div class="flex justify-around">
                                <div class="flex flex-col items-center">
                                    <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                        2
                                    </div>
                                    <h3 class="text-lg font-bold mb-2">Résidence principale en France ?</h3>
                                    <div class="flex gap-4">
                                        <button class="px-4 py-2 bg-[var(--color-success)] text-white rounded">Oui</button>
                                        <button class="px-4 py-2 bg-gray-300 rounded">Non</button>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col items-center">
                                    <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                        3
                                    </div>
                                    <h3 class="text-lg font-bold mb-2">Activité ≥50% en Suisse ?</h3>
                                    <div class="flex gap-4">
                                        <button class="px-4 py-2 bg-[var(--color-success)] text-white rounded">Oui</button>
                                        <button class="px-4 py-2 bg-gray-300 rounded">Non</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-around mt-8">
                                <div class="text-center p-4 bg-[var(--color-success-light)] rounded-lg">
                                    <h4 class="font-bold">Statut frontalier standard</h4>
                                    <p>Impôt à la source en Suisse</p>
                                </div>
                                
                                <div class="text-center p-4 bg-[var(--color-cta)] text-white rounded-lg">
                                    <h4 class="font-bold">Statut Quasi-Résident</h4>
                                    <p>Imposition en France</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Onglet Social -->
            <div class="p-8 tab-content" id="social">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-gray-800">Social</h1>
                    <p class="text-[var(--color-text-secondary)] mt-2">Comparateurs et outils pour la protection sociale</p>
                </header>

                <section class="rounded-lg bg-white p-6 shadow-sm mb-8">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">Comparateur LAMal vs CMU</h2>
                        <p class="text-[var(--color-text-secondary)]">Trouvez la meilleure couverture santé pour votre situation</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="font-bold mb-3">Profil du client</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Âge</label>
                                    <input type="number" value="38" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Revenu annuel (CHF)</label>
                                    <input type="number" value="120000" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Situation familiale</label>
                                    <select class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                        <option>Célibataire</option>
                                        <option selected>Marié(e), 2 enfants</option>
                                        <option>Marié(e), 1 enfant</option>
                                        <option>Concubinage</option>
                                    </select>
                                </div>
                                
                                <button class="w-full mt-2 rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white hover:bg-[var(--color-primary)] transition-colors">
                                    Calculer
                                </button>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-2">
                            <div class="overflow-x-auto">
                                <table class="w-full text-left">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th class="p-3">Critère</th>
                                            <th class="p-3 text-center">LAMal</th>
                                            <th class="p-3 text-center">CMU</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-b">
                                            <td class="p-3 font-medium">Coût annuel estimé</td>
                                            <td class="p-3 text-center">
                                                <div class="flex items-center justify-center gap-2">
                                                    <div class="flex">
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-secondary)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-secondary)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-secondary)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-secondary)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-gray-300 mx-0.5"></div>
                                                    </div>
                                                    <span>24 000 CHF</span>
                                                </div>
                                            </td>
                                            <td class="p-3 text-center">
                                                <div class="flex items-center justify-center gap-2">
                                                    <div class="flex">
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-success)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-[var(--color-success)] mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-gray-300 mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-gray-300 mx-0.5"></div>
                                                        <div class="w-3 h-3 rounded-full bg-gray-300 mx-0.5"></div>
                                                    </div>
                                                    <span>18 000 CHF</span>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="p-3 font-medium">Niveau de couverture</td>
                                            <td class="p-3 text-center">
                                                <div class="flex items-center justify-center gap-2">
                                                    ★★★★☆
                                                </div>
                                            </td>
                                            <td class="p-3 text-center">
                                                <div class="flex items-center justify-center gap-2">
                                                    ★★★☆☆
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="p-3 font-medium">Franchise standard</td>
                                            <td class="p-3 text-center">300 CHF</td>
                                            <td class="p-3 text-center">0 CHF</td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="p-3 font-medium">Prise en charge à l'étranger</td>
                                            <td class="p-3 text-center text-green-600">
                                                <svg class="h-5 w-5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </td>
                                            <td class="p-3 text-center text-red-600">
                                                <svg class="h-5 w-5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </td>
                                        </tr>
                                        <tr class="border-b">
                                            <td class="p-3 font-medium">Délai de résiliation</td>
                                            <td class="p-3 text-center">3 mois</td>
                                            <td class="p-3 text-center">12 mois</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="mt-6">
                                <h4 class="font-bold mb-2">Recommandation</h4>
                                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                    <p>Pour votre profil, la CMU offre un meilleur rapport qualité-prix. Cependant, si vous voyagez fréquemment à l'étranger, la LAMal est préférable.</p>
                                </div>
                            </div>
                            
                            <div class="mt-6 flex justify-end">
                                <button class="flex items-center gap-2 rounded-lg bg-[var(--color-cta)] px-4 py-2 text-white shadow-sm transition-colors hover:bg-orange-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Générer la Fiche de Recommandation (PDF)
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <section class="rounded-lg bg-white p-6 shadow-sm">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Simulateur d'optimisation retraite</h2>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Âge actuel</label>
                                <input type="number" value="38" class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Revenu annuel moyen (CHF)</label>
                                <input type="number" value="120000" class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">% investi dans le 3ème pilier</label>
                                <input type="range" min="0" max="30" value="10" class="w-full">
                                <div class="flex justify-between text-sm text-[var(--color-text-secondary)]">
                                    <span>0%</span>
                                    <span>10%</span>
                                    <span>20%</span>
                                    <span>30%</span>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Âge de départ à la retraite</label>
                                <input type="number" value="65" class="w-full rounded-lg border-[var(--color-border)] p-2 text-lg focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                            </div>
                        </div>
                        
                        <div>
                            <div class="bg-gray-50 rounded-lg p-6 h-full">
                                <h3 class="text-xl font-bold mb-4">Projection de retraite</h3>
                                
                                <div class="mb-6">
                                    <div class="flex justify-between mb-2">
                                        <span>Retraite AVS/AI</span>
                                        <span class="font-bold">42 000 CHF/an</span>
                                    </div>
                                    <div class="h-4 w-full bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full bg-[var(--color-secondary)]" style="width: 70%"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-6">
                                    <div class="flex justify-between mb-2">
                                        <span>2ème pilier</span>
                                        <span class="font-bold">28 000 CHF/an</span>
                                    </div>
                                    <div class="h-4 w-full bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full bg-[var(--color-success)]" style="width: 46%"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-8">
                                    <div class="flex justify-between mb-2">
                                        <span>3ème pilier (avec optimisation)</span>
                                        <span class="font-bold">18 000 CHF/an</span>
                                    </div>
                                    <div class="h-4 w-full bg-gray-200 rounded-full overflow-hidden">
                                        <div class="h-full bg-[var(--color-cta)]" style="width: 30%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <p class="text-sm text-[var(--color-text-secondary)]">Revenu annuel total à la retraite</p>
                                    <p class="text-3xl font-bold text-[var(--color-primary)]">88 000 CHF</p>
                                    <p class="mt-2 text-sm">Soit 75% de votre revenu actuel</p>
                                </div>
                                
                                <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-start gap-2">
                                        <svg class="h-5 w-5 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <p>Le 3ème pilier vous fait économiser <span class="font-bold">1 850 CHF/an</span> d'impôts dès aujourd'hui.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Onglet Dépôt de Documents -->
            <div class="p-8 tab-content" id="depot">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-gray-800">Dépôt de Documents</h1>
                    <div class="flex items-center gap-2 mt-2 text-sm text-[var(--color-text-secondary)]">
                        <svg class="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>Connexion sécurisée (SSL) - Vos documents sont protégés</span>
                    </div>
                </header>

                <section class="rounded-lg bg-white p-6 shadow-sm mb-8">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">Déposer un document</h2>
                        <p class="text-[var(--color-text-secondary)]">Sélectionnez un client pour déposer des documents</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="font-bold mb-3">Sélection du client</h3>
                            
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Client</label>
                                <select class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                    <option>Sélectionnez un client...</option>
                                    <option selected>Martin Dupont</option>
                                    <option>Sophie Bernard</option>
                                    <option>Thomas Moreau</option>
                                    <option>Camille Lefevre</option>
                                </select>
                            </div>
                            
                            <div class="text-sm">
                                <p class="font-medium">Informations client</p>
                                <ul class="mt-1 space-y-1 text-[var(--color-text-secondary)]">
                                    <li>ID Client: MC-4582</li>
                                    <li>Canton: Genève</li>
                                    <li>Dernier dépôt: 12/06/2025</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-2">
                            <div class="drag-drop-area">
                                <div class="mb-4">
                                    <svg class="h-12 w-12 mx-auto text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold mb-2">Glissez vos documents ici</h3>
                                <p class="text-[var(--color-text-secondary)] mb-4">Formats acceptés : PDF, JPG, PNG (max. 10 Mo par fichier)</p>
                                <button class="rounded-lg bg-white border border-[var(--color-border)] px-4 py-2 text-[var(--color-text-secondary)] hover:bg-gray-50">
                                    Ou parcourir vos fichiers
                                </button>
                            </div>
                            
                            <div class="mt-6">
                                <h3 class="font-bold mb-3">Documents en cours de dépôt</h3>
                                <div class="border border-[var(--color-border)] rounded-lg">
                                    <div class="p-3 border-b flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-5 w-5 text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Déclaration_impots_2024.pdf</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-[var(--color-text-secondary)]">12.4 Mo</span>
                                            <button class="text-red-500">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-3 flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-5 w-5 text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Relevé_bancaire_Q2_2025.pdf</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-[var(--color-text-secondary)]">8.2 Mo</span>
                                            <button class="text-red-500">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 flex justify-end">
                                <button class="rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white hover:bg-[var(--color-primary)] transition-colors">
                                    Envoyer les documents
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <section class="rounded-lg bg-white p-6 shadow-sm">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Documents récents</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr class="border-b border-[var(--color-border)] bg-gray-50">
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Document</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Client</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Date de dépôt</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Statut</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-[var(--color-border)]">
                                <tr>
                                    <td class="px-4 py-3 font-medium">Avis d'imposition 2024</td>
                                    <td class="px-4 py-3">Martin Dupont</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">15/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-completed">Traité</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Télécharger</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Contrat de travail</td>
                                    <td class="px-4 py-3">Sophie Bernard</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">14/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-inprogress">En vérification</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Voir</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Relevé bancaire Q2</td>
                                    <td class="px-4 py-3">Thomas Moreau</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">10/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-completed">Traité</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Télécharger</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Justificatif domicile</td>
                                    <td class="px-4 py-3">Camille Lefevre</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">08/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-waiting">En attente</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Voir</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
            </div>

            <!-- Onglet Actualités -->
            <div class="p-8 tab-content" id="actualites">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-gray-800">Actualités</h1>
                    <p class="text-[var(--color-text-secondary)] mt-2">Les dernières informations sur la fiscalité transfrontalière</p>
                </header>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-secondary)] text-white text-xs rounded">Fiscalité</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">10/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Nouveaux seuils pour le télétravail des frontaliers à Genève</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Les autorités genevoises ont annoncé une augmentation du nombre de jours de télétravail autorisés pour les frontaliers, passant de 34 à 45 jours par an à compter du 1er janvier 2026.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-success)] text-white text-xs rounded">Social</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">08/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Réforme des cotisations sociales pour les travailleurs frontaliers</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Une nouvelle convention entre la France et la Suisse modifie les règles de cotisation sociale pour les frontaliers. Découvrez les impacts sur votre protection sociale.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-cta)] text-white text-xs rounded">Échéance</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">05/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Déclaration fiscale 2025 : dates importantes à retenir</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Le calendrier des déclarations fiscales pour 2025 vient d'être publié. Notez dès maintenant les dates limites pour les résidents et frontaliers.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-secondary)] text-white text-xs rounded">Fiscalité</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">02/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Optimisation fiscale : les nouvelles stratégies pour 2025</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Découvrez les dernières stratégies d'optimisation fiscale pour les travailleurs frontaliers suite aux changements législatifs en France et en Suisse.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1589156280159-27698a70f29e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-success)] text-white text-xs rounded">Retraite</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">28/06/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Réforme des retraites : impacts pour les frontaliers</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">La réforme des retraites en France aura des conséquences spécifiques pour les travailleurs frontaliers. Analyse des changements et stratégies d'adaptation.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-cta)] text-white text-xs rounded">Formation</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">25/06/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Nouveau module de formation : optimisation transfrontalière</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Notre nouveau module de formation sur les dernières techniques d'optimisation fiscale transfrontalière est désormais disponible pour tous les collaborateurs.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8 text-center">
                    <button class="rounded-lg border border-[var(--color-border)] bg-white px-4 py-2 text-[var(--color-text-secondary)] shadow-sm transition-colors hover:bg-gray-50">
                        Charger plus d'actualités
                    </button>
                </div>
            </div>

            <!-- Onglet FAQ -->
            <div class="p-8 tab-content" id="faq">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-gray-800">Foire Aux Questions</h1>
                    <p class="text-[var(--color-text-secondary)] mt-2">Trouvez des réponses aux questions fréquentes sur la fiscalité transfrontalière</p>
                </header>

                <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                    <div class="border-b">
                        <div class="faq-question" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span>Quelles sont les conditions pour bénéficier du statut de quasi-résident à Genève ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Pour bénéficier du statut de quasi-résident à Genève, vous devez remplir les conditions suivantes :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Être domicilié fiscalement en France</li>
                                <li>Travailler principalement dans le canton de Genève (au moins 90% de votre activité professionnelle)</li>
                                <li>Ne pas avoir eu votre résidence principale en Suisse durant les 5 dernières années</li>
                                <li>Disposer d'une résidence permanente en France</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span>Comment déclarer mes revenus suisses en France ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Les revenus suisses doivent être déclarés en France selon les modalités suivantes :</p>
                            <ol class="list-decimal pl-5 mt-2 space-y-1">
                                <li>Déclarez vos revenus bruts suisses dans la catégorie "Traitements et salaires"</li>
                                <li>Appliquez un abattement de 10% pour frais professionnels</li>
                                <li>Déduisez les cotisations sociales effectivement payées en Suisse</li>
                                <li>Mentionnez l'impôt à la source payé en Suisse pour bénéficier du crédit d'impôt</li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="social">
                            <div class="flex justify-between items-center">
                                <span>Quelle assurance maladie choisir en tant que frontalier ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>En tant que frontalier, vous avez le choix entre :</p>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div class="border p-4 rounded-lg">
                                    <h4 class="font-bold mb-2">LAMal (Suisse)</h4>
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li>Couverture étendue en Suisse</li>
                                        <li>Prise en charge à l'étranger limitée</li>
                                        <li>Coût généralement plus élevé</li>
                                        <li>Délai de résiliation : 3 mois</li>
                                    </ul>
                                </div>
                                <div class="border p-4 rounded-lg">
                                    <h4 class="font-bold mb-2">CMU (France)</h4>
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li>Couverture valable en France et Suisse</li>
                                        <li>Coût généralement moins élevé</li>
                                        <li>Prise en charge à l'étranger limitée</li>
                                        <li>Délai de résiliation : 12 mois</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="retraite">
                            <div class="flex justify-between items-center">
                                <span>Comment fonctionne la retraite pour les travailleurs frontaliers ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Le système de retraite des travailleurs frontaliers est régi par la convention franco-suisse :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Vous cotisez au 1er pilier suisse (AVS) et au 2ème pilier (LPP)</li>
                                <li>Vous cotisez également au système français pour la retraite de base et complémentaire</li>
                                <li>Au moment de la retraite, vous percevrez une pension de chaque pays</li>
                                <li>Le 3ème pilier suisse est un excellent outil d'optimisation fiscale</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span>Puis-je déduire mes frais de transport entre la France et la Suisse ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Oui, les frais de transport entre votre domicile français et votre lieu de travail en Suisse sont déductibles :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Pour les résidents français : déduction possible dans la limite de 4 872€ par an</li>
                                <li>Pour les quasi-résidents : déduction intégrale des frais réels</li>
                                <li>Conservez tous vos justificatifs (billets, abonnements, factures de carburant)</li>
                                <li>Les frais de péage et de parking sont également déductibles</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="documents">
                            <div class="flex justify-between items-center">
                                <span>Quels documents dois-je fournir pour mon dossier fiscal ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Pour constituer un dossier fiscal complet, vous devez fournir :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Votre contrat de travail suisse</li>
                                <li>Vos fiches de salaire de l'année</li>
                                <li>Votre avis d'imposition français de l'année précédente</li>
                                <li>Votre attestation de sécurité sociale</li>
                                <li>Les justificatifs de frais professionnels (transport, repas, formation)</li>
                                <li>Votre attestation de situation familiale</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Vous ne trouvez pas de réponse ?</h2>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <form>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre nom</label>
                                    <input type="text" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre email</label>
                                    <input type="email" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Catégorie de question</label>
                                <select class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                    <option>Sélectionnez une catégorie...</option>
                                    <option>Fiscalité</option>
                                    <option>Protection sociale</option>
                                    <option>Retraite</option>
                                    <option>Documents</option>
                                    <option>Autre</option>
                                </select>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre question</label>
                                <textarea rows="4" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]"></textarea>
                            </div>
                            
                            <button class="rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white hover:bg-[var(--color-primary)] transition-colors">
                                Envoyer votre question
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Gestion des onglets
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                
                // Masquer tous les contenus d'onglet
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // Afficher le contenu de l'onglet sélectionné
                document.getElementById(tabId).classList.add('active');
                
                // Mettre à jour l'onglet actif dans la barre latérale
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('bg-sky-100', 'text-[var(--color-primary)]');
                    btn.classList.add('text-[var(--color-text-secondary)]');
                });
                
                this.classList.add('bg-sky-100', 'text-[var(--color-primary)]');
                this.classList.remove('text-[var(--color-text-secondary)]');
            });
        });

        // Gestion des FAQ
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const answer = this.nextElementSibling;
                const icon = this.querySelector('svg');
                
                if (answer.style.display === 'block') {
                    answer.style.display = 'none';
                    icon.classList.remove('rotate-180');
                } else {
                    answer.style.display = 'block';
                    icon.classList.add('rotate-180');
                }
            });
        });

        // Zone de drag and drop
        const dropArea = document.querySelector('.drag-drop-area');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropArea.classList.add('dragover');
        }
        
        function unhighlight() {
            dropArea.classList.remove('dragover');
        }
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        function handleFiles(files) {
            alert(`Vous avez déposé ${files.length} fichier(s)`);
        }
    </script>
</body>
</html>